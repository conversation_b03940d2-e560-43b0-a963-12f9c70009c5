/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_3912360763")

  // remove field
  collection.fields.removeById("text3809902195")

  // add field
  collection.fields.addAt(3, new Field({
    "hidden": false,
    "id": "json24311493",
    "maxSize": 0,
    "name": "billingDetails",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "json"
  }))

  // add field
  collection.fields.addAt(4, new Field({
    "hidden": false,
    "id": "json1878135002",
    "maxSize": 0,
    "name": "shippingDetails",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "json"
  }))

  // add field
  collection.fields.addAt(5, new Field({
    "hidden": false,
    "id": "json651465777",
    "maxSize": 0,
    "name": "productDetails",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "json"
  }))

  // add field
  collection.fields.addAt(6, new Field({
    "hidden": false,
    "id": "json1691220712",
    "maxSize": 0,
    "name": "bankDetails",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "json"
  }))

  // add field
  collection.fields.addAt(7, new Field({
    "hidden": false,
    "id": "json11120568",
    "maxSize": 0,
    "name": "termsConditions",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "json"
  }))

  // add field
  collection.fields.addAt(8, new Field({
    "hidden": false,
    "id": "file3796675911",
    "maxSelect": 1,
    "maxSize": *********,
    "mimeTypes": [],
    "name": "digitalSignature",
    "presentable": false,
    "protected": false,
    "required": false,
    "system": false,
    "thumbs": [],
    "type": "file"
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_3912360763")

  // add field
  collection.fields.addAt(1, new Field({
    "autogeneratePattern": "",
    "hidden": false,
    "id": "text3809902195",
    "max": 0,
    "min": 0,
    "name": "billNo",
    "pattern": "",
    "presentable": false,
    "primaryKey": false,
    "required": false,
    "system": false,
    "type": "text"
  }))

  // remove field
  collection.fields.removeById("json24311493")

  // remove field
  collection.fields.removeById("json1878135002")

  // remove field
  collection.fields.removeById("json651465777")

  // remove field
  collection.fields.removeById("json1691220712")

  // remove field
  collection.fields.removeById("json11120568")

  // remove field
  collection.fields.removeById("file3796675911")

  return app.save(collection)
})
