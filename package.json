{"name": "invoice-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.11.2", "jspdf": "^3.0.1", "lucide-react": "^0.541.0", "next": "15.5.0", "next-themes": "^0.4.6", "pocketbase": "^0.26.2", "react": "19.1.0", "react-dom": "19.1.0", "react-loading-indicators": "^1.0.1", "react-to-print": "^3.1.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7"}}