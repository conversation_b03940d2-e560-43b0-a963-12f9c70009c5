import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import CompanyDetails from "./CompanyDetails";
import BankDetails from "./BankDetails";
import DigitalSignature from "./DigitalSignature";
import TermsConditions from "./TermsConditions";
import LogoInput from "./LogoInput";

export default function NewOrganisation({
  logo,
  setLogo,
  companyDetails,
  setCompanyDetails,
  bankDetails,
  setBankingDetails,
  termsConditions,
  setTermsConditions,
  digitalSignature,
  setDigitalSignature,
  loadingState,
  handleAdd,
}) {
  return (
    <Card className={"p-4"}>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Company Details */}
        <CompanyDetails
          formData={companyDetails}
          setFormData={setCompanyDetails}
        />

        {/* Bank Details */}
        <BankDetails formData={bankDetails} setFormData={setBankingDetails} />

        {/* Digital Signature */}
        <LogoInput image={logo} setImage={setLogo} />
      </div>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Terms & Condition */}
        <TermsConditions
          termsArray={termsConditions}
          setTermsArray={setTermsConditions}
        />
        {/* Digital Signature */}
        <DigitalSignature
          image={digitalSignature}
          setImage={setDigitalSignature}
        />
      </div>

      <Button disabled={loadingState} onClick={handleAdd}>
        {loadingState ? "Creating Organisation" : "Create Organisation"}
      </Button>
    </Card>
  );
}
