"use client";

import { useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { createOrganisation } from "@/lib/apis/organisation";
import NewOrganisation from "./components/NewOrganisation";

export default function CreateOrganisation() {
  const { user } = useAuth();
  const [companyDetails, setCompanyDetails] = useState({
    name: "",
    address: "",
    gstin: "",
    contact: "",
    email: "",
  });
  const [bankDetails, setBankingDetails] = useState({
    name: "",
    accountNo: "",
    ifsc: "",
  });
  const [termsConditions, setTermsConditions] = useState([]);
  const [digitalSignature, setDigitalSignature] = useState(null);
  const [logo, setLogo] = useState(null);
  const [loadingState, setLoadingState] = useState(false);

  const handleReset = () => {
    setCompanyDetails({
      name: "",
      address: "",
      gstin: "",
      contact: "",
      email: "",
    });
    setBankingDetails({
      name: "",
      accountNo: "",
      ifsc: "",
    });
    setTermsConditions([]);
    setDigitalSignature(null);
  };

  const handleAdd = async (e) => {
    e.preventDefault();
    if (!user?.id && !user?.role === "admin") {
      toast.error("Please login before adding organisations.");
      return;
    }
    try {
      setLoadingState(true);
      const formData = new FormData();
      const { name, address, gstin, contact, email } = companyDetails;
      // Append serialized data
      formData.append("name", name);
      formData.append("address", address);
      formData.append("gstin", gstin);
      formData.append("contact", contact);
      formData.append("email", email);
      formData.append("bankDetails", JSON.stringify(bankDetails));
      formData.append("termsConditions", JSON.stringify(termsConditions));
      // Append digital signature (if provided)
      if (digitalSignature) {
        formData.append("signature", digitalSignature);
      }
      // Append logo (if provided)
      if (logo) {
        formData.append("logo", logo);
      }

      // Submit
      await createOrganisation(formData);
      handleReset();
      toast.success("Successfully created the organisation!!!");
      setLoadingState(false);
    } catch (error) {
      toast.error("Error adding organisation, please try again later...");
      setLoadingState(false);
    }
  };

  return (
    <main className="p-4 w-full">
      <div className="w-full py-4 px-8">
        <Link href={"/organisations"}>
          <Button variant={"outline"} className={"text-muted-foreground"}>
            <ChevronLeft size={18} /> Back to Organisations
          </Button>
        </Link>
        <h1 className="block md:hidden text-2xl font-semibold text-center">
          New Organisation
        </h1>
        <h1 className="hidden md:block text-2xl font-semibold text-center">
          Create a new Organisation
        </h1>
      </div>
      <div className="grid gap-4 p-4">
        <NewOrganisation
          logo={logo}
          setLogo={setLogo}
          companyDetails={companyDetails}
          setCompanyDetails={setCompanyDetails}
          bankDetails={bankDetails}
          setBankingDetails={setBankingDetails}
          termsConditions={termsConditions}
          setTermsConditions={setTermsConditions}
          digitalSignature={digitalSignature}
          setDigitalSignature={setDigitalSignature}
          loadingState={loadingState}
          handleAdd={handleAdd}
        />
      </div>
    </main>
  );
}
