"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { MoreVertical } from "lucide-react";
import { useEffect, useState } from "react";
import Link from "next/link";
import {
  deleteOrganisation,
  fetchOrganisations,
} from "@/lib/apis/organisation";
import Image from "next/image";
import { API_BASE_URL } from "@/constants/url";
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuContent,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";

export default function OrganisationsPage() {
  const [search, setSearch] = useState("");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const records = await fetchOrganisations();
        setData(records?.output);
      } catch (error) {
        console.log("Failed to fetch organisations:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const filtered = data?.filter(
    (item) =>
      !search ||
      JSON.stringify(item?.name)?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="flex items-center justify-center p-6">
      <div className="w-[90%]">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold">Welcome back!</h2>
            <p className="text-sm text-muted-foreground">
              Here's a list of organisations.
            </p>
          </div>

          <Link href={"/organisations/new"}>
            <Button>Add Organisation</Button>
          </Link>
        </div>

        <div className="w-full flex gap-2 mb-4">
          <Input
            placeholder="Search Organisations..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full"
          />
        </div>

        {loading ? (
          <p>Loading organisations...</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className={"text-center"}>Company</TableHead>
                <TableHead className={"w-[150px]"}>Address</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Bank Details</TableHead>
                <TableHead className={"w-[150px]"}>
                  Terms / Conditions
                </TableHead>
                <TableHead></TableHead>
                <TableHead />
              </TableRow>
            </TableHeader>
            <TableBody>
              {filtered?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="flex items-center gap-4">
                      {item?.logo && (
                        <div>
                          <Image
                            src={`${API_BASE_URL}${item?.logo}`}
                            alt="Logo"
                            width={5000}
                            height={5000}
                            className="w-12 rounded-full"
                          />
                        </div>
                      )}
                      <div>
                        <p>{item?.name || "Company"}</p>
                        {item?.gstin && (
                          <Badge variant="outline">{item?.gstin || ""}</Badge>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{item?.address}</TableCell>
                  <TableCell>{item?.contact}</TableCell>
                  <TableCell>{item?.email}</TableCell>
                  <TableCell>
                    {item?.bankDetails?.name ||
                    item?.bankDetails?.accountNo ||
                    item?.bankDetails?.ifsc ? (
                      <div className="grid gap-2">
                        <Badge variant={"outline"}>
                          Name - {item?.bankDetails?.name}
                        </Badge>
                        <Badge variant={"outline"}>
                          Account No - {item?.bankDetails?.accountNo}
                        </Badge>
                        <Badge variant={"outline"}>
                          IFSC - {item?.bankDetails?.ifsc}
                        </Badge>
                      </div>
                    ) : (
                      "N/A"
                    )}
                  </TableCell>
                  <TableCell>
                    {item?.termsConditions?.map((terms, index) => (
                      <Badge
                        variant={"outline"}
                        key={index}
                        className={"line-clamp-1"}
                      >
                        {terms}
                      </Badge>
                    ))}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger>
                        <MoreVertical size={18} />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-56">
                        <DropdownMenuLabel className={"text-muted-foreground"}>
                          Actions
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <Link href={`/organisations/edit/${item?.id}`}>
                          <DropdownMenuItem>Edit</DropdownMenuItem>
                        </Link>
                        <DropdownMenuItem
                          onClick={async () => {
                            const confirmation = confirm(
                              "Are you sure you want to delete this entry?"
                            );
                            if (confirmation) {
                              try {
                                const result = await deleteOrganisation(
                                  item?.id
                                );
                                if (result?.returncode === 200) {
                                  toast.success(
                                    "Successfully deleted the Organisation"
                                  );
                                } else {
                                  toast.warning(result?.message);
                                }
                              } catch (error) {
                                toast?.error(
                                  "Error occured while deleting, please try again later..."
                                );
                              }
                            }
                          }}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}
