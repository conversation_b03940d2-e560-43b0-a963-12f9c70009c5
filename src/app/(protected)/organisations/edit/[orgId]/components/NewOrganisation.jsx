import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import CompanyDetails from "./CompanyDetails";
import BankDetails from "./BankDetails";
import DigitalSignature from "./DigitalSignature";
import TermsConditions from "./TermsConditions";
import LogoInput from "./LogoInput";

export default function NewOrganisation({
  logo,
  logoUrl,
  setLogo,
  companyDetails,
  setCompanyDetails,
  bankDetails,
  setBankingDetails,
  termsConditions,
  setTermsConditions,
  digitalSignature,
  digitalSignatureUrl,
  setDigitalSignature,
  loadingState,
  handleAdd,
  handleUpdate,
  update = false,
}) {
  return (
    <Card className={"p-4"}>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Company Details */}
        <CompanyDetails
          formData={companyDetails}
          setFormData={setCompanyDetails}
        />

        {/* Bank Details */}
        <BankDetails formData={bankDetails} setFormData={setBankingDetails} />

        {/* Digital Signature */}
        <LogoInput image={logo} setImage={setLogo} editPreviewUrl={logoUrl} />
      </div>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Terms & Condition */}
        <TermsConditions
          termsArray={termsConditions}
          setTermsArray={setTermsConditions}
        />
        {/* Digital Signature */}
        <DigitalSignature
          image={digitalSignature}
          setImage={setDigitalSignature}
          editPreviewUrl={digitalSignatureUrl}
        />
      </div>
      {update ? (
        <Button disabled={loadingState} onClick={handleUpdate}>
          {loadingState ? "Updating Details" : "Update Details"}
        </Button>
      ) : (
        <Button disabled={loadingState} onClick={handleAdd}>
          {loadingState ? "Creating Organisation" : "Create Organisation"}
        </Button>
      )}
    </Card>
  );
}
