import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

export default function CompanyDetails({ formData, setFormData }) {
  const [errorText, setErrorText] = useState({
    name: "",
    address: "",
    gstin: "",
    contact: "",
    email: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Validation logic
    let error = "";

    switch (name) {
      case "name":
        if (/^[a-zA-ZÀ-ÿ0-9\s&',().\-\/]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid name.";
        }
        break;

      case "contact":
        if (/^\d{0,10}$/.test(value)) {
          if (value.length === 10) {
            error = "";
            setFormData((prev) => ({ ...prev, [name]: value }));
          } else if (value.length < 10) {
            setFormData((prev) => ({ ...prev, [name]: value }));
            value?.length === 0
              ? (error = "")
              : (error = "Contact must be exactly 10 digits");
          } else {
            error = "Contact must be exactly 10 digits";
          }
        }
        break;

      case "gstin":
        const gstinValue = value.toUpperCase();

        if (gstinValue.length === 0) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        } else if (gstinValue.length < 15) {
          error = "GSTIN must be 15 characters";
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        } else if (gstinValue.length > 15) {
          error = "GSTIN must be 15 characters";
        } else if (
          !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(
            gstinValue
          )
        ) {
          error = "Invalid GSTIN format";
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        } else {
          error = ""; // Fully valid
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        }
        break;

      case "email":
        if (value.toLowerCase()) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Invalid email address";
        }
        break;

      default:
        error = "";
        setFormData((prev) => ({ ...prev, [name]: value }));
    }

    setErrorText((prev) => ({ ...prev, [name]: error }));
  };

  return (
    <div className="w-full border rounded-md p-4">
      <h1 className="text-lg font-medium mb-6"> Company Details </h1>
      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Name</Label>
          <Input
            type={"text"}
            name="name"
            placeholder="eg; D-Mart"
            value={formData.name}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.name}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Contact</Label>
          <Input
            type={"text"}
            name="contact"
            placeholder="eg; 1234567890"
            value={formData.contact}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.contact}</p>
        </div>
      </div>

      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>GSTIN</Label>
          <Input
            type={"text"}
            name="gstin"
            placeholder="eg; 09AAACH7409R1ZZ"
            value={formData.gstin}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.gstin}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Email</Label>
          <Input
            type={"email"}
            name="email"
            placeholder="eg; <EMAIL>"
            value={formData.email}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.email}</p>
        </div>
      </div>

      <div className="w-full grid gap-2">
        <Label>Address</Label>
        <Textarea
          name="address"
          placeholder="Please enter a valid address"
          value={formData.address}
          onChange={handleChange}
        />
        <p className="text-xs text-destructive">{errorText.address}</p>
      </div>
    </div>
  );
}
