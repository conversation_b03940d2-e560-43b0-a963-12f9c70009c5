import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

export default function BankDetails({ formData, setFormData }) {
  const [errorText, setErrorText] = useState({
    name: "",
    accountNo: "",
    ifsc: "",
  });

  const handleChange = (e) => {
    let { name, value } = e.target;
    let error = "";

    // Auto-uppercase IFSC input
    if (name === "ifsc") {
      value = value.toUpperCase();
    }

    switch (name) {
      case "name":
        // Allow letters, numbers, common bank name characters
        if (/^[a-zA-ZÀ-ÿ0-9\s&',().\-\/]+$/.test(value) || value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid Bank Name";
        }
        break;

      case "accountNo":
        // Allow 9–18 digit numeric account number
        if (/^\d{9,18}$/.test(value) || value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (/^\d{1,8}$/.test(value) || value === "") {
          error = "Account number must be 9 to 18 digits";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Account number must be 9 to 18 digits";
        }
        break;

      case "ifsc": {
        value = value.toUpperCase();

        if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value.length > 11) {
          error = "IFSC can only be 11 characters";
        } else if (!/^[A-Z0-9]*$/.test(value)) {
          error = "Only letters and numbers allowed";
        } else if (
          value.length === 11 &&
          !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(value)
        ) {
          error = "Enter a valid IFSC code (e.g., SBIN0000123)";
        } else {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        }

        break;
      }

      default:
        error = "";
    }

    // Update error text
    setErrorText((prev) => ({
      ...prev,
      [name]: error,
    }));
  };

  return (
    <div className="w-full border rounded-md p-4">
      <h1 className="text-lg font-medium mb-6"> Bank Details </h1>
      <div className="grid grid-cols-1 gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Bank Name</Label>
          <Input
            type={"text"}
            name="name"
            placeholder="eg; ABC Bank"
            value={formData.name}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.name}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Account No.</Label>
          <Input
            type={"text"}
            name="accountNo"
            placeholder="eg; *************"
            value={formData.accountNo}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.accountNo}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>IFSC Code</Label>
          <Input
            type={"text"}
            name="ifsc"
            placeholder="eg; ABC0000003"
            value={formData.ifsc}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.ifsc}</p>
        </div>
      </div>
    </div>
  );
}
