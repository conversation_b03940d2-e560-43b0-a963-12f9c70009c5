"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import {
  fetchOrganisations,
  updateOrganisation,
  updateOrgFilesUrl,
} from "@/lib/apis/organisation";
import NewOrganisation from "./components/NewOrganisation";
import { useParams, useRouter } from "next/navigation";
import { API_BASE_URL } from "@/constants/url";

export default function CreateOrganisation() {
  const { orgId } = useParams(); // Access dynamic route param
  const { user } = useAuth();
  const router = useRouter();
  const [companyDetails, setCompanyDetails] = useState({
    name: "",
    address: "",
    gstin: "",
    contact: "",
    email: "",
  });
  const [bankDetails, setBankingDetails] = useState({
    name: "",
    accountNo: "",
    ifsc: "",
  });
  const [termsConditions, setTermsConditions] = useState([]);
  const [digitalSignature, setDigitalSignature] = useState(null);
  const [digitalSignatureUrl, setDigitalSignatureUrl] = useState(null);
  const [logo, setLogo] = useState(null);
  const [logoUrl, setLogoUrl] = useState(null);
  const [loadingState, setLoadingState] = useState(false);
  const [found, setFound] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await fetchOrganisations();
        if (result?.returncode === 200) {
          const org = result?.output?.find((o) => o.id === orgId);
          setCompanyDetails({
            name: org?.name || "",
            address: org?.address || "",
            gstin: org?.gstin || "",
            contact: org?.contact || "",
            email: org?.email || "",
          });
          setBankingDetails({
            name: org?.bankDetails?.name || "",
            accountNo: org?.bankDetails?.accountNo || "",
            ifsc: org?.bankDetails?.ifsc || "",
          });
          setTermsConditions(org?.termsConditions || []);
          setDigitalSignatureUrl(
            org?.signature ? `${API_BASE_URL}${org?.signature}` : null
          );
          setLogoUrl(org?.logo ? `${API_BASE_URL}${org?.logo}` : null);
          setLoading(false);
          setFound(true);
        } else {
          setLoading(false);
          setFound(false);
        }
      } catch (error) {
        setLoading(false);
        setFound(false);
        toast.error("Error fetching details");
      }
    };
    if (orgId) {
      fetchData();
    }
  }, [orgId]);

  const handleReset = () => {
    setCompanyDetails({
      name: "",
      address: "",
      gstin: "",
      contact: "",
      email: "",
    });
    setBankingDetails({
      name: "",
      accountNo: "",
      ifsc: "",
    });
    setTermsConditions([]);
    setDigitalSignature(null);
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    if (!user?.id && !user?.role === "admin") {
      toast.error("Please login before adding organisations.");
      return;
    }

    try {
      setLoadingState(true);
      const data = {
        orgId,
        ...companyDetails,
        termsConditions,
        bankDetails,
      };
      const formData = new FormData();
      // If Files are given
      if (digitalSignature || logo) {
        formData.append("orgId", orgId);
        // Append digital signature (if provided)
        if (digitalSignature) {
          formData.append("signature", digitalSignature);
        }
        // Append logo (if provided)
        if (logo) {
          formData.append("logo", logo);
        }
        // Submit
        await updateOrgFilesUrl(formData);
      }

      // Submit
      await updateOrganisation(data);
      router.push("/organisations");
      handleReset();
      toast.success("Successfully updated the organisation!!!");
      setLoadingState(false);
    } catch (error) {
      toast.error("Error updating organisation, please try again later...");
      setLoadingState(false);
    }
  };

  if (!found && !loading) {
    return (
      <main className="min-h-screen flex flex-col gap-2 items-center justify-center">
        <h1>Sorry, didn't find the organisation you're looking for.</h1>
        <Link href="/organisations">
          <Button>
            Go to Organisations <ArrowRightFromLine size={18} />
          </Button>
        </Link>
      </main>
    );
  }

  return (
    <main className="p-4 w-full">
      <div className="w-full py-4 px-8">
        <Link href={"/organisations"}>
          <Button variant={"outline"} className={"text-muted-foreground"}>
            <ChevronLeft size={18} /> Back to Organisations
          </Button>
        </Link>
        <h1 className="text-2xl font-semibold text-center">
          Update Organisation Info
        </h1>
      </div>
      <div className="grid gap-4 p-4">
        <NewOrganisation
          logo={logo}
          logoUrl={logoUrl}
          setLogo={setLogo}
          companyDetails={companyDetails}
          setCompanyDetails={setCompanyDetails}
          bankDetails={bankDetails}
          setBankingDetails={setBankingDetails}
          termsConditions={termsConditions}
          setTermsConditions={setTermsConditions}
          digitalSignature={digitalSignature}
          setDigitalSignature={setDigitalSignature}
          digitalSignatureUrl={digitalSignatureUrl}
          loadingState={loadingState}
          handleUpdate={handleUpdate}
          update
        />
      </div>
    </main>
  );
}
