'use client';

import NewInvoice from "@/components/sections/home/<USER>";
import { useState } from "react";
import { toast } from "sonner";
import { pocketbaseInstance } from "@/lib/db";

export default function Home() {
  const today = new Date().toISOString().split('T')[0];
  const [companyDetails, setCompanyDetails] = useState({
    name: '',
    address: '',
    gstin: '',
    contact: '',
    email: ''
  });
  const [invoiceDetails, setInvoiceDetails] = useState({
    serialNo: '',
    invoiceDate: today,
    dueDate: today,
    note: ''
  });
  const [billingDetails, setBillingDetails] = useState({
    name: '',
    address: '',
    pincode: '',
    state: '',
    city: '',
    country: '',
    gstin: '',
  });
  const [shippingDetails, setShippingDetails] = useState({
    name: '',
    address: '',
    pincode: '',
    state: '',
    city: '',
    country: '',
    gstin: '',
  });
  const [bankDetails, setBankingDetails] = useState({
    name: '',
    accountNo: '',
    ifsc: '',
  });
  const [productDetails, setProductDetails] = useState([]);
  const [termsConditions, setTermsConditions] = useState([]);
  const [digitalSignature, setDigitalSignature] = useState(null);
  const [loadingState, setLoadingState] = useState(false);

  const handleReset = () => {
    setCompanyDetails({ name: '', address: '', gstin: '', contact: '', mail: '' });
    setInvoiceDetails({ serialNo: '', invoiceDate: today, dueDate: today, note: '' });
    setBillingDetails({ name: '', address: '', pincode: '', state: '', city: '', country: '', gstin: '', });
    setShippingDetails({ name: '', address: '', pincode: '', state: '', city: '', country: '', gstin: '', });
    setBankingDetails({ name: '', accountNo: '', ifsc: '', });
    setProductDetails([]);
    setTermsConditions([]);
    setDigitalSignature(null);
  }

  const handleAdd = async (e) => {
    e.preventDefault();
    try {
      setLoadingState(true);
      const formData = new FormData();
      // Append serialized data
      formData.append('invoiceDetails', JSON.stringify(invoiceDetails));
      formData.append('companyDetails', JSON.stringify(companyDetails));
      formData.append('billingDetails', JSON.stringify(billingDetails));
      formData.append('shippingDetails', JSON.stringify(shippingDetails));
      formData.append('productDetails', JSON.stringify(productDetails));
      formData.append('bankDetails', JSON.stringify(bankDetails));
      formData.append('termsConditions', JSON.stringify(termsConditions));
      // Append digital signature (if provided)
      if (digitalSignature) {
        formData.append('digitalSignature', digitalSignature);
      }

      // Submit to Pocketbase
      await pocketbaseInstance.collection('bills').create(formData);
      handleReset();
      toast.success('Successfully created the invoice!!!');
      setLoadingState(false);
    } catch (error) {
      console.error('Error creating invoice:', error);
      toast.error('Error adding invoice, please try again later...');
      setLoadingState(false);
    }
  };

  return (
    <main className="p-4 w-full">
      <div className="flex items-center justify-between w-full py-4 px-8">
        <h1 className="block md:hidden text-2xl font-semibold text-center">New Invoice</h1>
        <h1 className="hidden md:block text-2xl font-semibold text-center">Create a new Invoice</h1>
      </div>
      <div className="grid gap-4 p-4">
        <NewInvoice
          companyDetails={companyDetails}
          setCompanyDetails={setCompanyDetails}
          invoiceDetails={invoiceDetails}
          setInvoiceDetails={setInvoiceDetails}
          billingDetails={billingDetails}
          setBillingDetails={setBillingDetails}
          shippingDetails={shippingDetails}
          setShippingDetails={setShippingDetails}
          bankDetails={bankDetails}
          setBankingDetails={setBankingDetails}
          productDetails={productDetails}
          setProductDetails={setProductDetails}
          termsConditions={termsConditions}
          setTermsConditions={setTermsConditions}
          digitalSignature={digitalSignature}
          setDigitalSignature={setDigitalSignature}
          loadingState={loadingState}
          handleAdd={handleAdd}
        />
      </div>
    </main>
  );
};
