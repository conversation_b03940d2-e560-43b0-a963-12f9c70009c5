"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ArrowUpRight, MoreVertical, Trash } from "lucide-react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { fetchInvoices } from "@/lib/apis/invoice";

export default function InvoicesPage() {
  const [search, setSearch] = useState("");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const records = await fetchInvoices();
        setData(records?.output);
      } catch (error) {
        console.error("Failed to fetch bills:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const filtered = data?.filter(
    (item) =>
      !search ||
      JSON.stringify(item?.invoiceDetails?.serialNo)
        ?.toLowerCase()
        .includes(search.toLowerCase()) ||
      JSON.stringify(item?.organisations?.name)
        ?.toLowerCase()
        .includes(search.toLowerCase())
  );
  return (
    <div className="flex items-center justify-center p-6">
      <div className="w-[90%]">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold">Welcome back!</h2>
            <p className="text-sm text-muted-foreground">
              Here's a list of your invoices.
            </p>
          </div>

          <Link href={"/invoices/new"}>
            <Button>Add Invoice</Button>
          </Link>
        </div>

        <div className="w-full flex gap-2 mb-4">
          <Input
            placeholder="Filter invoices..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full"
          />
        </div>

        {loading ? (
          <p>Loading invoices...</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>#</TableHead>
                <TableHead>Company Name</TableHead>
                <TableHead>Receiver</TableHead>
                <TableHead>Created By</TableHead>
                <TableHead>Invoice Date</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead></TableHead>
                <TableHead />
              </TableRow>
            </TableHeader>
            <TableBody>
              {filtered?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item?.invoiceDetails?.serialNo}</TableCell>
                  <TableCell>
                    <p>{item?.organisations?.name || "Company"}</p>
                    {item?.organisations?.gstin && (
                      <Badge variant="outline">
                        {item?.organisations?.gstin || ""}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <p>{item?.billingDetails?.name || "Company"}</p>
                    {item?.billingDetails?.gstin && (
                      <Badge variant="outline">
                        {item?.billingDetails?.gstin || ""}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {`${item?.users?.firstName} ${item?.users?.lastName}`}
                  </TableCell>
                  <TableCell>
                    {new Date(item.invoiceDetails?.invoiceDate).toDateString()}
                  </TableCell>
                  <TableCell>
                    {new Date(item.invoiceDetails?.dueDate).toDateString()}
                  </TableCell>
                  <TableCell>
                    <Link href={`/invoices/${item?.id}`}>
                      <Button variant="outline">
                        View Details <ArrowUpRight size={18} />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}
