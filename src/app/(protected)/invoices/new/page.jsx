"use client";

import NewInvoice from "@/components/sections/home/<USER>";
import { useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { createInvoice } from "@/lib/apis/invoice";

export default function Home() {
  const today = new Date().toISOString().split("T")[0];
  const { user } = useAuth();
  const [companyDetails, setCompanyDetails] = useState({
    name: "",
    address: "",
    gstin: "",
    contact: "",
    email: "",
  });
  const [invoiceDetails, setInvoiceDetails] = useState({
    serialNo: "",
    invoiceDate: today,
    dueDate: today,
    note: "",
    organisationId: "",
  });
  const [billingDetails, setBillingDetails] = useState({
    name: "",
    address: "",
    pincode: "",
    state: "",
    city: "",
    country: "",
    gstin: "",
  });
  const [shippingDetails, setShippingDetails] = useState({
    name: "",
    address: "",
    pincode: "",
    state: "",
    city: "",
    country: "",
    gstin: "",
  });
  const [bankDetails, setBankingDetails] = useState({
    name: "",
    accountNo: "",
    ifsc: "",
  });
  const [productDetails, setProductDetails] = useState([]);
  const [termsConditions, setTermsConditions] = useState([]);
  const [digitalSignature, setDigitalSignature] = useState(null);
  const [loadingState, setLoadingState] = useState(false);

  const handleReset = () => {
    setInvoiceDetails({
      serialNo: "",
      invoiceDate: today,
      dueDate: today,
      note: "",
      organisationId: "",
    });
    setBillingDetails({
      name: "",
      address: "",
      pincode: "",
      state: "",
      city: "",
      country: "",
      gstin: "",
    });
    setShippingDetails({
      name: "",
      address: "",
      pincode: "",
      state: "",
      city: "",
      country: "",
      gstin: "",
    });
  };

  const handleAdd = async (e) => {
    e.preventDefault();
    if (!user?.id) {
      toast.error("Please login before adding invoices.");
      return;
    }
    try {
      setLoadingState(true);
      const data = {
        organisationId: invoiceDetails.organisationId,
        invoiceDetails: invoiceDetails,
        billingDetails: billingDetails,
        shippingDetails: shippingDetails,
        productDetails: productDetails,
        createdBy: user.id,
      };

      // Submit to Pocketbase
      await createInvoice(data);
      handleReset();
      toast.success("Successfully created the invoice!!!");
      setLoadingState(false);
    } catch (error) {
      console.error("Error creating invoice:", error);
      toast.error("Error adding invoice, please try again later...");
      setLoadingState(false);
    }
  };

  return (
    <main className="p-4 w-full">
      <div className="w-full py-4 px-8">
        <Link href={"/invoices"}>
          <Button variant={"outline"} className={"text-muted-foreground"}>
            <ChevronLeft size={18} /> Back to Invoices
          </Button>
        </Link>
        <h1 className="block md:hidden text-2xl font-semibold text-center">
          New Invoice
        </h1>
        <h1 className="hidden md:block text-2xl font-semibold text-center">
          Create a new Invoice
        </h1>
      </div>
      <div className="grid gap-4 p-4">
        <NewInvoice
          invoiceDetails={invoiceDetails}
          setInvoiceDetails={setInvoiceDetails}
          billingDetails={billingDetails}
          setBillingDetails={setBillingDetails}
          shippingDetails={shippingDetails}
          setShippingDetails={setShippingDetails}
          productDetails={productDetails}
          setProductDetails={setProductDetails}
          loadingState={loadingState}
          handleAdd={handleAdd}
        />
      </div>
    </main>
  );
}
