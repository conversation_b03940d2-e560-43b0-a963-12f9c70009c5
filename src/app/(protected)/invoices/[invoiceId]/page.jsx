"use client";

import { useEffect, useState } from "react";
import InvoiceClientPage from "./components/InvoiceClientPage";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRightFromLine } from "lucide-react";
import { fetchInvoices } from "@/lib/apis/invoice";
import { useParams } from "next/navigation";
import { BlinkBlur } from "react-loading-indicators";

export default function InvoiceDisplay() {
  const { invoiceId } = useParams(); // Access dynamic route param
  const [invoiceInfo, setInvoiceInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadInvoices = async () => {
      setLoading(true);
      try {
        const invoicesData = await fetchInvoices();
        const invoices = invoicesData.output || [];
        const foundInvoice = invoices.find(
          (invoice) => invoice?.id === invoiceId
        );
        setInvoiceInfo(foundInvoice || null);
      } catch (err) {
        console.error("Error fetching invoices", err);
        setInvoiceInfo(null);
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId) {
      loadInvoices();
    }
  }, [invoiceId]);

  if (loading) {
    return (
      <main className="min-h-screen flex flex-col items-center justify-center">
        <BlinkBlur color="#1e73f8" size="large" text="" textColor="" />
      </main>
    );
  }

  if (invoiceInfo?.id) {
    return <InvoiceClientPage invoice={invoiceInfo} />;
  }

  return (
    <main className="min-h-screen flex flex-col gap-2 items-center justify-center">
      <h1>Sorry, didn't find the invoice you're looking for.</h1>
      <Link href="/bills">
        <Button>
          Go to Bills <ArrowRightFromLine size={18} />
        </Button>
      </Link>
    </main>
  );
}
