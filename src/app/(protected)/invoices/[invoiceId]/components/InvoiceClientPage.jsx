"use client";

import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { currencyFormatter } from "@/utils/currencyFormatter";
import Image from "next/image";
import { API_BASE_URL } from "@/constants/url";

export default function InvoiceClientPage({ invoice }) {
  const invoiceRef = useRef(null);
  let subTotal = 0,
    cgstAmt = 0,
    sgstAmt = 0,
    total = 0,
    quantity = 0;
  invoice?.productDetails?.forEach((product) => {
    subTotal += product?.subTotal || 0;
    cgstAmt += product?.cgstAmount || 0;
    sgstAmt += product?.sgstAmount || 0;
    total += product?.totalAmount || 0;
    quantity += product?.quantity || 0;
  });
  const signatureUrl = `${API_BASE_URL}${invoice?.organisations?.signature}`;

  // Print Invoice
  const handlePrint = () => {
    if (!invoiceRef.current) return;

    // Save the original page content
    const originalContents = document.body.innerHTML;

    // Replace the body with only the invoice content
    document.body.innerHTML = invoiceRef.current.innerHTML;

    // Trigger print
    window.print();

    // Restore original page after printing
    document.body.innerHTML = originalContents;

    // Ensure React can still bind events after restoring
    window.location.reload();
  };

  return (
    <div className="w-full max-w-5xl mx-auto px-4 py-6 space-y-6">
      {/* Action buttons */}
      <div className="flex justify-end gap-4 print:hidden w-full">
        <Button className={"w-full"} onClick={handlePrint}>
          Print <Printer size={18} />
        </Button>
      </div>

      <div
        ref={invoiceRef}
        className="print-area bg-white border rounded-md p-6 sm:p-8"
      >
        <div
          data-slot="card"
          className="w-full space-y-6 border-none shadow-none text-zinc-950 flex flex-col gap-6 rounded-xl border py-6"
        >
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
            <div className="text-blue-500 text-8xl font-bold leading-none">
              <Image
                src={`${API_BASE_URL}${invoice?.organisations?.logo}`}
                alt="Logo"
                width={5000}
                height={5000}
                className="w-[200px]"
              />
            </div>
            <div className="text-right">
              <h2 className="text-xl font-semibold">Business Invoice</h2>
              <p className="text-sm text-zinc-500">
                #{" "}
                <span className="font-medium">
                  {invoice?.invoiceDetails?.serialNo}
                </span>
              </p>
              <p className="text-sm text-zinc-500">
                Invoice date:{" "}
                {new Date(
                  invoice?.invoiceDetails?.invoiceDate
                ).toLocaleDateString()}
              </p>
              <p className="text-sm text-zinc-500">
                Due date:{" "}
                {new Date(
                  invoice?.invoiceDetails?.dueDate
                ).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Billing Info */}
          <div className="flex items-start justify-between gap-8">
            <div>
              <h3 className="font-medium text-sm">From</h3>
              <p className="font-semibold text-lg">
                {invoice?.organisations?.name}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.organisations?.gstin}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.organisations?.address}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.organisations?.email}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.organisations?.contact}
              </p>
            </div>
            <div>
              <h3 className="font-medium text-sm">Bill to</h3>
              <p className="font-semibold text-lg">
                {invoice?.billingDetails?.name}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.billingDetails?.gstin}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.billingDetails?.address}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.billingDetails?.city},{" "}
                {invoice?.billingDetails?.state} -{" "}
                {invoice?.billingDetails?.pincode}
              </p>
              <p className="text-sm text-zinc-500">
                {invoice?.billingDetails?.country}
              </p>
            </div>
            {invoice?.shippingDetails?.address && (
              <div>
                <h3 className="font-medium text-sm">Ship to</h3>
                <p className="font-semibold text-lg">
                  {invoice?.shippingDetails?.name ||
                    invoice?.billingDetails?.name}
                </p>
                <p className="text-sm text-zinc-500">
                  {invoice?.shippingDetails?.gstin}
                </p>
                <p className="text-sm text-zinc-500">
                  {invoice?.shippingDetails?.address}
                </p>
                <p className="text-sm text-zinc-500">
                  {invoice?.shippingDetails?.city},{" "}
                  {invoice?.shippingDetails?.state} -{" "}
                  {invoice?.shippingDetails?.pincode}
                </p>
                <p className="text-sm text-zinc-500">
                  {invoice?.shippingDetails?.country}
                </p>
              </div>
            )}
          </div>

          {/* Table */}
          <table className="table-auto text-left">
            <thead className="border-b">
              <tr>
                <th className="pb-2">#</th>
                <th className="pb-2">Product / Services</th>
                <th className="pb-2">Rate</th>
                <th className="pb-2">Qty</th>
                <th className="pb-2">Tax</th>
              </tr>
            </thead>
            <tbody>
              {invoice?.productDetails?.map((product, index) => (
                <tr key={product?.id || index} className="border-b">
                  <td>{product?.serialNo}</td>
                  <td className="grid gap-1 pb-2">
                    <p>{product?.name}</p>
                    <p className="text-xs text-zinc-500 line-clamp-1">
                      {product?.description}
                    </p>
                  </td>
                  <td>{product?.subTotal?.toFixed(2)}</td>
                  <td>{product?.quantity}</td>
                  <td>{product?.gstRate?.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Payment + Total */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
            <div>
              {invoice?.organisations?.bankDetails && (
                <>
                  <h4 className="font-semibold pb-2">Payment instruction</h4>
                  {invoice?.organisations?.bankDetails?.name && (
                    <>
                      <p className="text-sm font-medium">Bank Name:</p>
                      <p className="pb-2">
                        {" "}
                        {invoice?.organisations?.bankDetails?.name}
                      </p>
                    </>
                  )}
                  {invoice?.organisations?.bankDetails?.accountNo && (
                    <>
                      <p className="text-sm font-medium">Account No:</p>
                      <p className="pb-2">
                        {" "}
                        {invoice?.organisations?.bankDetails?.accountNo}
                      </p>
                    </>
                  )}
                  {invoice?.organisations?.bankDetails?.ifsc && (
                    <>
                      <p className="text-sm font-medium">IFSC Code:</p>
                      <p className="pb-2">
                        {" "}
                        {invoice?.organisations?.bankDetails?.ifsc}
                      </p>
                    </>
                  )}
                </>
              )}
              {invoice?.organisations?.termsConditions && (
                <>
                  <h4 className="font-semibold mt-8">Terms & Conditions</h4>
                  {invoice?.organisations?.termsConditions?.map(
                    (item, index) => (
                      <p key={index} className="text-sm text-zinc-500">
                        - {item}
                      </p>
                    )
                  )}
                </>
              )}
            </div>

            <div className="space-y-1">
              <div className="flex justify-between">
                <p>Subtotal ({quantity}):</p>
                <span className="font-medium">
                  {currencyFormatter.format(subTotal)}
                </span>
              </div>
              <div className="flex justify-between">
                <p>CGST:</p>
                <span className="font-medium">
                  {currencyFormatter.format(cgstAmt)}
                </span>
              </div>
              <div className="flex justify-between">
                <p>SGST:</p>
                <span className="font-medium">
                  {currencyFormatter.format(sgstAmt)}
                </span>
              </div>
              <div className="flex justify-end">
                <div className="border-b w-[200px]" />
              </div>
              <div className="flex justify-between text-lg font-semibold">
                <p>Total:</p>
                <span>{currencyFormatter.format(total)}</span>
              </div>
              <div className="flex justify-between font-semibold text-zinc-500">
                <p>Balance Due:</p>
                <span>{currencyFormatter.format(total)}</span>
              </div>
              {signatureUrl && (
                <div className="flex justify-end mt-4">
                  <img
                    src={signatureUrl}
                    alt="signature"
                    className="h-20 w-auto"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
