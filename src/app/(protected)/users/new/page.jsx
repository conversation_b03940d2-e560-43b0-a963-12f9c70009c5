"use client";

import { useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import NewUser from "./components/NewUser";

export default function CreateUser() {
  const { user, Register } = useAuth();
  const [userDetails, setUserDetails] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    username: "",
    contact: "",
    address: "",
    role: "",
  });
  const [avatar, setAvatar] = useState(null);
  const [loadingState, setLoadingState] = useState(false);

  const handleReset = () => {
    setUserDetails({
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      username: "",
      contact: "",
      address: "",
      role: "",
    });
    setAvatar(null);
  };

  const handleAdd = async (e) => {
    e.preventDefault();
    if (!user?.id && !user?.role === "admin") {
      toast.error("Please login before adding organisations.");
      return;
    }
    try {
      setLoadingState(true);
      const formData = new FormData();
      const {
        firstName,
        lastName,
        email,
        password,
        username,
        contact,
        address,
        role,
      } = userDetails;
      // Append serialized data
      formData.append("firstName", firstName);
      formData.append("lastName", lastName);
      formData.append("email", email);
      formData.append("password", password);
      formData.append("username", username);
      formData.append("contact", contact);
      formData.append("address", address);
      formData.append("role", role);

      // Append avatar (if provided)
      if (avatar) {
        formData.append("avatar", avatar);
      }

      // Submit
      await Register(formData);
      handleReset();
      toast.success("Successfully created the user!!!");
      setLoadingState(false);
    } catch (error) {
      toast.error("Error adding user, please try again later...");
      setLoadingState(false);
    }
  };

  return (
    <main className="p-4 w-full">
      <div className="w-full py-4 px-8">
        <Link href={"/users"}>
          <Button variant={"outline"} className={"text-muted-foreground"}>
            <ChevronLeft size={18} /> Back to Users
          </Button>
        </Link>
        <h1 className="block md:hidden text-2xl font-semibold text-center">
          New User
        </h1>
        <h1 className="hidden md:block text-2xl font-semibold text-center">
          Create a new User
        </h1>
      </div>
      <div className="grid gap-4 p-4">
        <NewUser
          logo={avatar}
          setLogo={setAvatar}
          userDetails={userDetails}
          setUserDetails={setUserDetails}
          loadingState={loadingState}
          handleAdd={handleAdd}
        />
      </div>
    </main>
  );
}
