import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";

export default function UserDetails({ formData, setFormData }) {
  const [errorText, setErrorText] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    username: "",
    contact: "",
    address: "",
  });
  const [showPass, setShowPass] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Validation logic
    let error = "";

    switch (name) {
      case "firstName":
        if (/^[a-zA-ZÀ-ÿ0-9&',().\-\/]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid name.";
        }
        break;

      case "lastName":
        if (/^[a-zA-ZÀ-ÿ0-9&',().\-\/]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid name.";
        }
        break;

      case "contact":
        if (/^\d{0,10}$/.test(value)) {
          if (value.length === 10) {
            error = "";
            setFormData((prev) => ({ ...prev, [name]: value }));
          } else if (value.length < 10) {
            setFormData((prev) => ({ ...prev, [name]: value }));
            value?.length === 0
              ? (error = "")
              : (error = "Contact must be exactly 10 digits");
          } else {
            error = "Contact must be exactly 10 digits";
          }
        }
        break;

      case "username":
        if (/^[a-zA-ZÀ-ÿ0-9&',().\-_\/]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid username.";
        }
        break;

      default:
        error = "";
        setFormData((prev) => ({ ...prev, [name]: value }));
    }

    setErrorText((prev) => ({ ...prev, [name]: error }));
  };

  return (
    <div className="w-full border rounded-md p-4">
      <h1 className="text-lg font-medium mb-6"> Company Details </h1>
      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>First Name</Label>
          <Input
            type={"text"}
            name="firstName"
            placeholder="eg; John"
            value={formData.firstName}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.firstName}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Last Name</Label>
          <Input
            type={"text"}
            name="lastName"
            placeholder="eg; Doe"
            value={formData.lastName}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.lastName}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>User Name</Label>
          <Input
            type={"text"}
            name="username"
            placeholder="eg; john_doe01"
            value={formData.username}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.lastName}</p>
        </div>
      </div>

      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Contact</Label>
          <Input
            type={"text"}
            name="contact"
            placeholder="eg; 1234567890"
            value={formData.contact}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.contact}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Role</Label>
          <Select
            value={formData?.role}
            onValueChange={(value) => setFormData({ ...formData, role: value })}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Role</SelectLabel>
                <SelectItem value={"admin"}>Admin</SelectItem>
                <SelectItem value={"user"}>User</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <p className="text-xs text-destructive">{errorText.contact}</p>
        </div>
      </div>

      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Email</Label>
          <Input
            type={"email"}
            name="email"
            placeholder="eg; <EMAIL>"
            value={formData.email}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.email}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Password</Label>
          <div className="flex items-center gap-3">
            <Input
              name="password"
              type={showPass ? "text" : "password"}
              onChange={handleChange}
              value={formData.password}
              placeholder="******"
              required
            />
            {showPass ? (
              <Eye size={18} onClick={() => setShowPass(false)} />
            ) : (
              <EyeOff size={18} onClick={() => setShowPass(true)} />
            )}
          </div>

          <p className="text-xs text-destructive">{errorText.contact}</p>
        </div>
      </div>

      <div className="w-full grid gap-2">
        <Label>Address</Label>
        <Textarea
          name="address"
          placeholder="Please enter a valid address"
          value={formData.address}
          onChange={handleChange}
        />
        <p className="text-xs text-destructive">{errorText.address}</p>
      </div>
    </div>
  );
}
