import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import UserDetails from "./UserDetails";
import AvatarInput from "./AvatarInput";

export default function NewUser({
  logo,
  setLogo,
  userDetails,
  setUserDetails,
  loadingState,
  handleAdd,
}) {
  return (
    <Card className={"p-4"}>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Company Details */}
        <UserDetails formData={userDetails} setFormData={setUserDetails} />

        {/* Digital Signature */}
        <AvatarInput image={logo} setImage={setLogo} />
      </div>

      <Button disabled={loadingState} onClick={handleAdd}>
        {loadingState ? "Creating User" : "Create User"}
      </Button>
    </Card>
  );
}
