"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRightFromLine, ChevronLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import NewUser from "./components/NewUser";
import { fetchUsers } from "@/lib/apis/users";
import { useParams } from "next/navigation";

export default function CreateUser() {
  const { userId } = useParams(); // Access dynamic route param
  const { user } = useAuth();
  const [userDetails, setUserDetails] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    username: "",
    contact: "",
    address: "",
    role: "",
  });
  const [avatar, setAvatar] = useState(null);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [loadingState, setLoadingState] = useState(false);
  const [loading, setLoading] = useState(true);
  const [found, setFound] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await fetchUsers();
        if (result?.returncode === 200) {
          const user = result?.output?.find((o) => o.id === userId);
          setUserDetails({
            firstName: user?.firstName || "",
            lastName: user?.lastName || "",
            email: user?.email || "",
            password: user?.password || "",
            username: user?.username || "",
            contact: user?.contact || "",
            address: user?.address || "",
            role: user?.role || "",
          });
          setAvatarUrl(user?.avatar ? `${API_BASE_URL}${user?.avatar}` : null);
          setLoading(false);
          setFound(true);
        } else {
          setLoading(false);
          setFound(false);
        }
      } catch (error) {
        setLoading(false);
        setFound(false);
        toast.error("Error fetching details");
      }
    };
    if (userId) {
      fetchData();
    }
  }, [userId]);

  const handleReset = () => {
    setUserDetails({
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      username: "",
      contact: "",
      address: "",
      role: "",
    });
    setAvatar(null);
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    if (!user?.id && !user?.role === "admin") {
      toast.error("Please login before adding organisations.");
      return;
    }
    try {
      setLoadingState(true);
      const formData = new FormData();
      const {
        firstName,
        lastName,
        email,
        password,
        username,
        contact,
        address,
        role,
      } = userDetails;
      // Append serialized data
      formData.append("firstName", firstName);
      formData.append("lastName", lastName);
      formData.append("email", email);
      formData.append("password", password);
      formData.append("username", username);
      formData.append("contact", contact);
      formData.append("address", address);
      formData.append("role", role);

      // Append avatar (if provided)
      if (avatar) {
        formData.append("avatar", avatar);
      }

      // Submit
      await Register(formData);
      handleReset();
      toast.success("Successfully created the user!!!");
      setLoadingState(false);
    } catch (error) {
      toast.error("Error adding user, please try again later...");
      setLoadingState(false);
    }
  };

  if (!found && !loading) {
    return (
      <main className="min-h-screen flex flex-col gap-2 items-center justify-center">
        <h1>Sorry, didn't find the user you're looking for.</h1>
        <Link href="/users">
          <Button>
            Go to Users <ArrowRightFromLine size={18} />
          </Button>
        </Link>
      </main>
    );
  }

  return (
    <main className="p-4 w-full">
      <div className="w-full py-4 px-8">
        <Link href={"/users"}>
          <Button variant={"outline"} className={"text-muted-foreground"}>
            <ChevronLeft size={18} /> Back to Users
          </Button>
        </Link>
        <h1 className="block md:hidden text-2xl font-semibold text-center">
          New User
        </h1>
        <h1 className="hidden md:block text-2xl font-semibold text-center">
          Create a new User
        </h1>
      </div>
      <div className="grid gap-4 p-4">
        <NewUser
          logo={avatar}
          avatarUrl={avatarUrl}
          setLogo={setAvatar}
          userDetails={userDetails}
          setUserDetails={setUserDetails}
          loadingState={loadingState}
          handleUpdate={handleUpdate}
        />
      </div>
    </main>
  );
}
