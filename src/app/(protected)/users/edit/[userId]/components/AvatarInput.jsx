import { useRef, useState } from "react";
import { Label } from "@/components/ui/label";
import { UploadCloud } from "lucide-react";

export default function AvatarInput({ image, setImage, imageUrl }) {
  const fileInputRef = useRef(null);
  const [dragActive, setDragActive] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);

  const handleFile = (file) => {
    if (file && file.type.startsWith("image/")) {
      setImage(file); // store actual File object
      setPreviewUrl(URL.createObjectURL(file)); // for preview
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    const file = e.dataTransfer.files[0];
    handleFile(file);
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    handleFile(file);
  };

  return (
    <div className="w-1/4 border rounded-md p-4 space-y-4">
      <Label className="block text-md font-medium">Avatar</Label>

      <div
        className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition ${
          dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300"
        }`}
        onClick={() => fileInputRef.current.click()}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="w-full flex items-center justify-center text-gray-500">
          <UploadCloud size={150} />
        </div>
        <p className="text-sm text-gray-500">
          Drag & drop an image here, or{" "}
          <span className="text-blue-600 underline">click to upload</span>
        </p>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileChange}
        />
      </div>

      {imageUrl && !previewUrl && (
        <div className="border p-2 rounded-md max-w-xs">
          <img src={imageUrl} alt="Uploaded" className="w-full rounded" />
        </div>
      )}

      {previewUrl && (
        <div className="border p-2 rounded-md max-w-xs">
          <img src={previewUrl} alt="Uploaded" className="w-full rounded" />
        </div>
      )}
    </div>
  );
}
