import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import UserDetails from "./UserDetails";
import AvatarInput from "./AvatarInput";

export default function NewUser({
  logo,
  avatarUrl,
  setLogo,
  userDetails,
  setUserDetails,
  loadingState,
  handleUpdate,
}) {
  return (
    <Card className={"p-4"}>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Company Details */}
        <UserDetails formData={userDetails} setFormData={setUserDetails} />

        {/* Digital Signature */}
        <AvatarInput image={logo} setImage={setLogo} imageUrl={avatarUrl} />
      </div>

      <Button disabled={loadingState} onClick={handleUpdate}>
        {loadingState ? "Updating User" : "Update User"}
      </Button>
    </Card>
  );
}
