import { Outfit } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import Header from "@/components/blocks/Header";
import { Toaster } from "sonner";
import { AuthContextProvider } from "@/contexts/AuthContext";

const outfitFont = Outfit({
  subsets: ["latin"],
});

export const metadata = {
  title: "Invoice Generator",
  description: "A Dynamic Invoice Generator Web-App",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${outfitFont.className} antialiased`}>
        <AuthContextProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <Toaster />
            {children}
          </ThemeProvider>
        </AuthContextProvider>
      </body>
    </html>
  );
}
