import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

export default function BillingDetails({ formData, setFormData }) {
  const [errorText, setErrorText] = useState({
    name: "",
    address: "",
    pincode: "",
    state: "",
    city: "",
    country: "",
    gstin: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Validation logic
    let error = "";
    switch (name) {
      case "name":
        if (/^[a-zA-ZÀ-ÿ0-9\s&',().\-\/]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid Name";
        }
        break;

      case "pincode": {
        const rawValue = value;
        const sanitized = rawValue.replace(/\s/g, "");

        // Allow only digits with optional one space (between 3rd and 4th digit)
        const isValidTyping = /^\d{0,3}\s?\d{0,3}$/.test(rawValue);

        if (isValidTyping && sanitized.length <= 6) {
          setFormData((prev) => ({ ...prev, [name]: rawValue }));

          if (sanitized.length === 0) {
            error = "";
          } else if (sanitized.length < 6) {
            error = "Pin Code must be 6 digits";
          } else if (!/^[1-9][0-9]{2}\s?[0-9]{3}$/.test(rawValue)) {
            error = "Enter a valid Pin Code";
          } else {
            error = "";
          }
        } else if (rawValue.length === 0) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: "" }));
        } else {
          error = "Only digits allowed, max 6 digits";
        }

        break;
      }

      case "gstin":
        const gstinValue = value.toUpperCase();

        if (gstinValue.length === 0) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        } else if (gstinValue.length < 15) {
          error = "GSTIN must be 15 characters";
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        } else if (gstinValue.length > 15) {
          error = "GSTIN must be 15 characters";
        } else if (
          !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(
            gstinValue
          )
        ) {
          error = "Invalid GSTIN format";
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        } else {
          error = ""; // Fully valid
          setFormData((prev) => ({ ...prev, [name]: gstinValue }));
        }
        break;

      case "state":
        if (/^[a-zA-ZÀ-ÿ\s'’.-]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid State Name";
        }
        break;

      case "city":
        if (/^[a-zA-ZÀ-ÿ\s'’.-]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid City Name";
        }
        break;

      case "country":
        if (/^[a-zA-ZÀ-ÿ\s'’.-]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: value }));
        } else {
          error = "Enter a valid Country Name";
        }
        break;

      default:
        error = "";
        setFormData((prev) => ({ ...prev, [name]: value }));
    }

    setErrorText((prev) => ({ ...prev, [name]: error }));
  };

  return (
    <div className="w-full border rounded-md p-4">
      <h1 className="text-lg font-medium mb-6"> Billing Details </h1>
      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Name (Company Name / Individual) </Label>
          <Input
            type={"text"}
            name="name"
            placeholder="eg; Pepsi"
            value={formData.name}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.name}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>GSTIN</Label>
          <Input
            type={"text"}
            name="gstin"
            placeholder="eg; 09AAACH7409R1ZZ"
            value={formData.gstin}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.gstin}</p>
        </div>
      </div>

      <div className="w-full grid gap-2 pb-4">
        <Label>Street Address</Label>
        <Textarea
          name="address"
          placeholder="Please enter a valid Street address"
          value={formData.address}
          onChange={handleChange}
        />
        <p className="text-xs text-destructive">{errorText.address}</p>
      </div>

      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>State</Label>
          <Input
            type={"text"}
            name="state"
            placeholder="eg; Maharashtra"
            value={formData.state}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.state}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>City</Label>
          <Input
            type={"text"}
            name="city"
            placeholder="eg; Mumbai"
            value={formData.city}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.city}</p>
        </div>
      </div>

      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Pincode</Label>
          <Input
            type={"text"}
            name="pincode"
            placeholder="eg; 400 021"
            value={formData.pincode}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.pincode}</p>
        </div>

        <div className="w-full grid gap-2">
          <Label>Country</Label>
          <Input
            type={"text"}
            name="country"
            placeholder="eg; India"
            value={formData.country}
            onChange={handleChange}
          />
          <p className="text-xs text-destructive">{errorText.country}</p>
        </div>
      </div>
    </div>
  );
}
