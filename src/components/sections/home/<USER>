import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useState } from "react";
import { fetchUserAccessByFilter } from "@/lib/apis/userAccess";
import { useAuth } from "@/contexts/AuthContext";
import { fetchOrganisations } from "@/lib/apis/organisation";
import Image from "next/image";
import { API_BASE_URL } from "@/constants/url";

export default function InvoiceDetails({ formData, setFormData }) {
  const { user } = useAuth();
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  const [orgsAccess, setOrgsAccess] = useState([]);
  const [organisations, setOrganisations] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      if (user?.role === "admin") {
        const organisations = await fetchOrganisations();
        if (organisations?.returncode === 200) {
          console.log(organisations?.output);
          setOrganisations(organisations.output);
        }
      } else {
        const accessLists = await fetchUserAccessByFilter(
          `?userId=${user?.id}`
        );
        console.log(accessLists);
      }
    };
    if (user?.id) {
      fetchData();
    }
  }, [user]);

  return (
    <div className="w-full border rounded-md p-4">
      <h1 className="text-lg font-medium mb-6"> Invoice Details </h1>
      <div className="w-full grid gap-2 pb-4">
        <Label>Serial No</Label>
        <Input
          type={"text"}
          name="serialNo"
          placeholder="eg; INV-001"
          value={formData.serialNo}
          onChange={handleChange}
        />
      </div>

      <div className="flex md:flex-row flex-col items-center justify-between gap-4 pb-4">
        <div className="w-full grid gap-2">
          <Label>Invoice Date</Label>
          <Input
            type={"date"}
            name="invoiceDate"
            value={formData.invoiceDate}
            onChange={handleChange}
          />
        </div>

        <div className="w-full grid gap-2">
          <Label>Due Date</Label>
          <Input
            type={"date"}
            name="dueDate"
            value={formData.dueDate}
            onChange={handleChange}
          />
        </div>
      </div>

      <div className="w-full grid gap-2 pb-4">
        <Label>Note</Label>
        <Textarea
          rows={6}
          name="note"
          placeholder="Enter an optional note"
          value={formData.note}
          onChange={handleChange}
        />
      </div>

      <div className="w-full grid gap-2">
        <Label>Organisation</Label>
        <Select
          value={formData?.organisationId}
          onValueChange={(value) =>
            setFormData({ ...formData, organisationId: value })
          }
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select an Organisation" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Organisations</SelectLabel>
              {user?.role === "admin" &&
                organisations.map((org, index) => (
                  <SelectItem key={index} value={org?.id}>
                    <Image
                      src={`${API_BASE_URL}${org?.logo}`}
                      alt="logo"
                      width={5000}
                      height={5000}
                      className="w-12"
                    />
                    {org.name}
                  </SelectItem>
                ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
