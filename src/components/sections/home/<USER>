import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export default function TermsConditions({ termsArray, setTermsArray }) {
  const [term, setTerm] = useState("");
  const [editIndex, setEditIndex] = useState(null);

  const handleAddOrUpdate = () => {
    if (!term.trim()) return;

    if (editIndex !== null) {
      const updated = [...termsArray];
      updated[editIndex] = term;
      setTermsArray(updated);
      setEditIndex(null);
    } else {
      setTermsArray([...termsArray, term]);
    }
    setTerm("");
  };

  const handleEdit = (index) => {
    setTerm(termsArray[index]);
    setEditIndex(index);
  };

  const handleDelete = (index) => {
    const updated = [...termsArray];
    updated.splice(index, 1);
    setTermsArray(updated);
    if (editIndex === index) {
      setTerm("");
      setEditIndex(null);
    }
  };

  return (
    <div className="w-1/2 border rounded-md p-4">
      <div className="mb-6">
        <h1 className="text-lg font-medium mb-6">Terms & Conditions</h1>
        <div className="grid gap-4 pb-4">
          <div className="w-full grid gap-2">
            <Label>Term</Label>
            <Input
              type="text"
              placeholder="Enter a term or condition"
              value={term}
              onChange={(e) => setTerm(e.target.value)}
            />
          </div>

          <div className="flex gap-4">
            <Button onClick={handleAddOrUpdate}>
              {editIndex !== null ? "Update Term" : "Add Term"}
            </Button>
            {editIndex !== null && (
              <Button variant="outline" onClick={() => {
                setTerm("");
                setEditIndex(null);
              }}>
                Cancel Edit
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Display terms */}
      <div>
        <h2 className="text-md font-medium mb-2">Terms List</h2>
        <div className="overflow-auto">
          <Table className="w-full border text-sm text-left">
            <TableHeader>
              <TableRow>
                <TableHead className="p-2 border">#</TableHead>
                <TableHead className="p-2 border">Term</TableHead>
                <TableHead className="p-2 border">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {termsArray.map((t, index) => (
                <TableRow key={index}>
                  <TableCell className="p-2 border">{index + 1}</TableCell>
                  <TableCell className="p-2 border">{t}</TableCell>
                  <TableCell className="p-2 border">
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(index)}>Edit</Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(index)}>Delete</Button>
                  </TableCell>
                </TableRow>
              ))}
              {termsArray.length === 0 && (
                <TableRow>
                  <TableCell colSpan="3" className="text-center p-2 border">No terms added yet.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
