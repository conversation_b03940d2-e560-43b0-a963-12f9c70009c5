import { Card } from "@/components/ui/card";
import InvoiceDetails from "./InvoiceDetails";
import BillingDetails from "./BillingDetails";
import ShippingDetails from "./ShippingDetails";
import ProductDetails from "./ProductDetails";
import { Button } from "@/components/ui/button";

export default function NewInvoice({
  invoiceDetails,
  setInvoiceDetails,
  billingDetails,
  setBillingDetails,
  shippingDetails,
  setShippingDetails,
  productDetails,
  setProductDetails,
  loadingState,
  handleAdd,
}) {
  return (
    <Card className={"p-4"}>
      <div className="w-full md:flex gap-4 md:justify-between">
        {/* Invoice Details */}
        <InvoiceDetails
          formData={invoiceDetails}
          setFormData={setInvoiceDetails}
        />
        {/* Billing Details */}
        <BillingDetails
          formData={billingDetails}
          setFormData={setBillingDetails}
        />

        {/* Shipping Details */}
        <ShippingDetails
          formData={shippingDetails}
          setFormData={setShippingDetails}
          billingDetails={billingDetails}
        />
      </div>

      {/* Product Details */}
      <ProductDetails
        productDetailsArray={productDetails}
        setProductDetailsArray={setProductDetails}
      />

      <Button disabled={loadingState} onClick={handleAdd}>
        {loadingState ? "Creating Invoice" : "Create Invoice"}
      </Button>
    </Card>
  );
}
