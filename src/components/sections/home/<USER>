import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Pencil, Trash } from "lucide-react";

export default function ProductDetails({
  productDetailsArray,
  setProductDetailsArray,
}) {
  const initialFormData = {
    serialNo: "",
    name: "",
    description: "",
    hsnsac: "",
    quantity: 1,
    subTotal: 0,
    gstRate: 0,
    gstAmount: 0,
    cgstRate: 0,
    cgstAmount: 0,
    sgstRate: 0,
    sgstAmount: 0,
    totalAmount: 0,
  };

  const [formData, setFormData] = useState(initialFormData);
  const [editIndex, setEditIndex] = useState(null);
  const [errorText, setErrorText] = useState({
    serialNo: "",
    name: "",
    description: "",
    hsnsac: "",
    quantity: "",
    subTotal: "",
    gstRate: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    let error = "";
    let newValue = value;

    switch (name) {
      case "serialNo":
        if (/^[a-zA-ZÀ-ÿ0-9\s&',().\-\/]+$/.test(value) || value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else {
          error = "Enter a valid Serial No";
        }
        break;

      case "name":
        if (/^[a-zA-ZÀ-ÿ0-9\s&',().\-\/]+$/.test(value)) {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else {
          error = "Enter a valid Name";
        }
        break;

      case "description":
        // Optional field, no strict validation
        error = "";
        break;

      case "hsnsac":
        if (/^\d{4,8}$/.test(value) || value === "") {
          error = "";
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else {
          error = "HSN/SAC must be 4 to 8 digits";
        }
        break;

      case "quantity": {
        const number = parseFloat(value);
        if (value === "") {
          error = "";
          newValue = 1;
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else if (!isNaN(number) && number > 0) {
          error = "";
          newValue = number;
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else {
          error = "Enter a valid number";
        }
        break;
      }

      case "subTotal": {
        const number = parseFloat(value);
        if (value === "") {
          error = "";
          newValue = 0;
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else if (!isNaN(number) && number >= 0) {
          error = "";
          newValue = number;
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else {
          error = "Enter a valid number";
        }
        break;
      }

      case "gstRate": {
        const number = parseFloat(value);
        if (value === "") {
          error = "";
          newValue = 0;
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else if (!isNaN(number) && number >= 0 && number <= 100) {
          error = "";
          newValue = number;
          setFormData((prev) => ({ ...prev, [name]: newValue }));
        } else {
          error = "GST rate must be between 0 and 100";
        }
        break;
      }

      default:
        error = "";
    }
    // Update error text
    setErrorText((prev) => ({
      ...prev,
      [name]: error,
    }));
  };

  // Auto-calculate CGST, SGST, GST Amounts, and Total
  useEffect(() => {
    const { gstRate, subTotal, quantity } = formData;
    const cgstRate = gstRate / 2;
    const sgstRate = gstRate / 2;
    const baseTotal = subTotal * quantity;
    const gstAmount = (baseTotal * gstRate) / 100;
    const cgstAmount = gstAmount / 2;
    const sgstAmount = cgstAmount;
    const totalAmount = baseTotal + gstAmount;

    setFormData((prev) => ({
      ...prev,
      cgstRate,
      sgstRate,
      gstAmount,
      cgstAmount,
      sgstAmount,
      totalAmount,
    }));
  }, [formData.gstRate, formData.subTotal, formData.quantity]);

  const handleAddOrUpdate = () => {
    if (editIndex !== null) {
      const updatedArray = [...productDetailsArray];
      updatedArray[editIndex] = formData;
      setProductDetailsArray(updatedArray);
      setEditIndex(null);
    } else {
      setProductDetailsArray([...productDetailsArray, formData]);
    }
    setFormData(initialFormData);
  };

  const handleEdit = (index) => {
    setFormData(productDetailsArray[index]);
    setEditIndex(index);
  };

  const handleDelete = (index) => {
    const updatedArray = [...productDetailsArray];
    updatedArray.splice(index, 1);
    setProductDetailsArray(updatedArray);
    if (editIndex === index) {
      setFormData(initialFormData);
      setEditIndex(null);
    }
  };

  return (
    <div className="w-full border rounded-md p-4">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-medium mb-6">
            Product / Service &nbsp; Details
          </h1>
          <div className="flex gap-4">
            <Button onClick={handleAddOrUpdate}>
              {editIndex !== null ? "Update Item" : "Add Item"}
            </Button>
            {editIndex !== null && (
              <Button
                variant="outline"
                onClick={() => {
                  setFormData(initialFormData);
                  setEditIndex(null);
                }}
              >
                Cancel Edit
              </Button>
            )}
          </div>
        </div>

        <div className="grid md:grid-cols-4 grid-cols-1 gap-4 pb-4">
          {[
            ["Serial No", "serialNo", "text", "eg; PRO-001"],
            ["Name", "name", "text", "eg; Landing Page"],
            [
              "Description",
              "Description",
              "text",
              "A one line description of Product / Service",
            ],
            ["HSN/SAC", "hsnsac", "text", "eg; 998313"],
            ["Quantity", "quantity", "number", ""],
            ["Sub Total", "subTotal", "number", ""],
            ["GST Rate (%)", "gstRate", "number", ""],
          ].map(([label, name, type, placeholder]) => (
            <div key={name} className="w-full grid gap-2">
              <Label>{label}</Label>
              <Input
                type={type}
                name={name}
                value={formData[name]}
                onChange={handleChange}
                placeholder={placeholder}
              />
              <p className="text-xs text-destructive">{errorText[name]}</p>
            </div>
          ))}

          <div className="w-full grid gap-2">
            <Label>Total Amount</Label>
            <Input
              type="number"
              name="totalAmount"
              value={formData.totalAmount.toFixed(2)}
              disabled
            />
          </div>
        </div>
      </div>

      {/* Table to display items */}
      <div>
        <h2 className="text-md font-medium mb-2">Product List</h2>
        <div className="overflow-auto">
          <Table className="w-full border rounded-md over text-sm">
            <TableHeader>
              <TableRow>
                <TableHead className="p-2 border">#</TableHead>
                <TableHead className="p-2 border">Name</TableHead>
                <TableHead className="p-2 border">HSN/SAC</TableHead>
                <TableHead className="p-2 border">Qty</TableHead>
                <TableHead className="p-2 border">Subtotal</TableHead>
                <TableHead className="p-2 border">GST %</TableHead>
                <TableHead className="p-2 border">GST Amt</TableHead>
                <TableHead className="p-2 border">CGST</TableHead>
                <TableHead className="p-2 border">SGST</TableHead>
                <TableHead className="p-2 border">Total</TableHead>
                <TableHead className="p-2 border">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {productDetailsArray.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="p-2 border">{index + 1}</TableCell>
                  <TableCell className="p-2 border">{item.name}</TableCell>
                  <TableCell className="p-2 border">{item.hsnsac}</TableCell>
                  <TableCell className="p-2 border">{item.quantity}</TableCell>
                  <TableCell className="p-2 border">
                    {item.subTotal.toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 border">{item.gstRate}%</TableCell>
                  <TableCell className="p-2 border">
                    {item.gstAmount.toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 border">
                    {item.cgstAmount.toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 border">
                    {item.sgstAmount.toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 border">
                    {item.totalAmount.toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 border">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(index)}
                    >
                      <Pencil className="size-4 text-primary" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(index)}
                    >
                      <Trash className="size-4 text-red-500" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              {productDetailsArray.length === 0 && (
                <TableRow>
                  <TableCell colSpan="11" className="text-center p-2 border">
                    No products added yet.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
