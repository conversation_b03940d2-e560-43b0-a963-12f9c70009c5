import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";

export function LoginForm({ className, ...props }) {
  const { Login } = useAuth();
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: "",
    pass: "",
  });
  const [showPass, setShowPass] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await <PERSON><PERSON>(formData.email, formData.pass);
      toast.success("Successfully Logged In!!");
      router.push("/dashboard");
    } catch (error) {
      toast.error(error);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle>Login to your account</CardTitle>
          <CardDescription>
            Enter your email below to login to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="password">Password</Label>
                <div className="flex items-center gap-3">
                  <Input
                    id="password"
                    type={showPass ? "text" : "password"}
                    value={formData.pass}
                    onChange={(e) =>
                      setFormData({ ...formData, pass: e.target.value })
                    }
                    placeholder="******"
                    required
                  />
                  {showPass ? (
                    <Eye size={18} onClick={() => setShowPass(false)} />
                  ) : (
                    <EyeOff size={18} onClick={() => setShowPass(true)} />
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-3">
                <Button type="submit" className="w-full">
                  Login
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
