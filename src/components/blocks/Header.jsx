"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ModeToggle } from "../ui/mode-toggle";
import Image from "next/image";

export default function Header() {
  const [nav, setNav] = useState(false);
  const pathname = usePathname();

  const navLinks = [
    { name: "Dashboard", href: "/dashboard" },
    { name: "Organisations", href: "/organisations" },
    { name: "Invoices", href: "/invoices" },
    { name: "Quotations", href: "/quotations" },
    { name: "Users", href: "/users" },
  ];

  return (
    <header>
      <nav className="bg-background border-b border-border px-4 lg:px-6 py-2.5 shadow">
        <div className="flex flex-wrap justify-between items-center mx-auto max-w-screen-lg">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src={"/logo.png"}
              alt="Logo"
              width={5000}
              height={5000}
              className="w-20"
            />
          </Link>

          {/* Nav Links */}
          <div
            className={`flex-col md:flex md:flex-row items-center w-full md:w-auto md:order-2 transition-all duration-300 ${
              nav
                ? "absolute top-14 left-0 w-full bg-background shadow-md p-4 md:relative md:top-0 md:w-auto md:bg-transparent md:shadow-none"
                : "hidden md:flex gap-6"
            }`}
          >
            <ul className="flex flex-col md:flex-row md:gap-8 gap-0">
              {navLinks.map((link) => {
                const isActive = pathname === link.href;
                return (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className={`block py-2 pr-4 pl-3 rounded transition-colors ${
                        isActive
                          ? "font-medium"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      {link.name}
                    </Link>
                  </li>
                );
              })}
              <li>
                <ModeToggle />
              </li>
            </ul>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center lg:order-1">
            <button
              type="button"
              className="inline-flex items-center p-2 ml-1 text-sm rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              aria-controls="mobile-menu"
              aria-expanded={nav}
              onClick={() => setNav(!nav)}
            >
              <span className="sr-only">Open main menu</span>
              {nav ? (
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 
                    0 111.414 1.414L11.414 10l4.293 
                    4.293a1 1 0 01-1.414 1.414L10 
                    11.414l-4.293 4.293a1 1 0 
                    01-1.414-1.414L8.586 10 4.293 
                    5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              ) : (
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 
                    011-1h12a1 1 0 110 
                    2H4a1 1 0 01-1-1zM3 10a1 
                    1 0 011-1h12a1 1 0 110 
                    2H4a1 1 0 01-1-1zM3 15a1 
                    1 0 011-1h12a1 1 0 110 
                    2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              )}
            </button>
          </div>
        </div>
      </nav>
    </header>
  );
}
