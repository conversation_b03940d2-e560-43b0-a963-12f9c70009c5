"use client";

import React, { useRef } from "react";
import { useReactToPrint } from "react-to-print";

// Sample product data
const products = [
  { name: "Veg Hakka Noodles", qty: 2, rate: 25, gst: 5 },
  { name: "LUX Soap", qty: 3, rate: 40, gst: 18 },
  { name: "Basmati Rice", qty: 1, rate: 300, gst: 5 },
  { name: "Detergent", qty: 2, rate: 150, gst: 12 },
];

const Receipt = () => {
  const receiptRef = useRef(null);

  // Calculate totals
  const subtotal = products.reduce((acc, p) => acc + p.qty * p.rate, 0);

  // Group GST breakup
  const gstBreakup = products.reduce((acc, p) => {
    const taxable = p.qty * p.rate;
    if (!acc[p.gst]) acc[p.gst] = { taxable: 0, cgst: 0, sgst: 0, total: 0 };
    const gstAmount = (taxable * p.gst) / 100;
    acc[p.gst].taxable += taxable;
    acc[p.gst].cgst += gstAmount / 2;
    acc[p.gst].sgst += gstAmount / 2;
    acc[p.gst].total += taxable + gstAmount;
    return acc;
  }, {});

  const total = Object.values(gstBreakup).reduce(
    (acc, val) => acc + val.total,
    0
  );

  // ✅ Correct way for v3
  const handlePrint = useReactToPrint({
    contentRef: receiptRef,
    documentTitle: "Receipt",
  });

  return (
    <div className="max-w-md mx-auto">
      {/* Print Button */}
      <div className="flex justify-end mb-4">
        <button
          onClick={handlePrint}
          className="bg-blue-600 text-white px-4 py-2 rounded-md shadow-md hover:bg-blue-700"
        >
          Print Receipt
        </button>
      </div>

      {/* Receipt */}
      <div
        ref={receiptRef}
        className="bg-white shadow-md p-4 font-mono text-sm print:w-full print:shadow-none"
      >
        <h2 className="text-center font-bold text-lg mb-2">D'Mart Supermarket</h2>
        <p className="text-center text-xs mb-4">Tax Invoice</p>

        {/* Product List */}
        <table className="w-full border-t border-b mb-4">
          <thead>
            <tr className="border-b">
              <th className="text-left">Item</th>
              <th>Qty</th>
              <th>Rate</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            {products.map((p, i) => (
              <tr key={i}>
                <td>{p.name}</td>
                <td className="text-center">{p.qty}</td>
                <td className="text-center">{p.rate}</td>
                <td className="text-right">{p.qty * p.rate}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* GST Section */}
        <h3 className="font-semibold mb-2">GST Breakup</h3>
        <table className="w-full border-t border-b mb-4">
          <thead>
            <tr className="border-b">
              <th>Rate%</th>
              <th>Taxable</th>
              <th>CGST</th>
              <th>SGST</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(gstBreakup).map(([rate, val], i) => (
              <tr key={i}>
                <td className="text-center">{rate}%</td>
                <td className="text-right">{val.taxable.toFixed(2)}</td>
                <td className="text-right">{val.cgst.toFixed(2)}</td>
                <td className="text-right">{val.sgst.toFixed(2)}</td>
                <td className="text-right">{val.total.toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Totals */}
        <div className="flex justify-between font-bold text-base">
          <span>Total:</span>
          <span>₹ {total.toFixed(2)}</span>
        </div>

        <p className="text-center text-xs mt-4">
          This is a computer generated invoice
        </p>
      </div>
    </div>
  );
};

export default Receipt;
