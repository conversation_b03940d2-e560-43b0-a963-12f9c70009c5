"use client";

import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export default function InvoicePreview() {
  const invoiceRef = useRef(null);

  // Print Invoice
  const handlePrint = () => {
    const content = invoiceRef.current.innerHTML;
    const printWindow = window.open("", "_blank");
    printWindow.document.write(`
    <html>
      <head>
        <title>Invoice</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link rel="stylesheet" href="/globals.css" />
      </head>
      <body class="p-6 bg-white text-black">
        ${content}
      </body>
    </html>
  `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="w-full bg-background max-w-5xl mx-auto px-4 py-6 space-y-6">
      {/* Action buttons */}
      <div className="flex justify-end gap-4 print:hidden">
        <Button onClick={handlePrint}>Print</Button>
      </div>

      {/* Invoice Content */}
      <div
        ref={invoiceRef}
        className="border rounded-md p-6 sm:p-8"
      >
        <Card className="w-full space-y-6 border-none shadow-none">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
            <div className="text-blue-500 text-5xl font-bold leading-none">SA</div>
            <div className="text-right">
              <h2 className="text-xl font-semibold">Small Business Invoice</h2>
              <p className="text-sm text-muted-foreground">
                Invoice no.: <span className="font-medium">001</span>
              </p>
              <p className="text-sm text-muted-foreground">
                Invoice date: Jul 13th, 2021
              </p>
              <p className="text-sm text-muted-foreground">
                Due: Feb 13th, 2021
              </p>
            </div>
          </div>

          <Separator />

          {/* Billing Info */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold">From</h3>
              <p className="font-medium">Saldo Apps</p>
              <p>John Smith</p>
              <p><EMAIL></p>
              <p>***********</p>
              <p>First str., 28-32, Chicago, USA</p>
            </div>
            <div>
              <h3 className="font-semibold">Bill to</h3>
              <p className="font-medium">Shepard corp.</p>
              <p><EMAIL></p>
              <p>***********</p>
              <p>North str., 32, Chicago, USA</p>
              <p className="mt-2 font-medium">Ship to</p>
              <p>North str., 32, Chicago, USA</p>
              <p>Track #: RO***********</p>
            </div>
          </div>

          <Separator />

          {/* Table */}
          <div>
            <div className="grid grid-cols-6 font-semibold text-sm border-b py-2 bg-muted">
              <div className="col-span-2">Description</div>
              <div>Rate</div>
              <div>Qty</div>
              <div>Tax</div>
              <div>Disc</div>
            </div>

            {/* Row 1 */}
            <div className="grid grid-cols-6 text-sm border-b py-2">
              <div className="col-span-2">
                Prototype
                <p className="text-xs text-muted-foreground">
                  Prototype-based programming is a style of object-oriented programming
                </p>
              </div>
              <div>20,230,450.00</div>
              <div>2000</div>
              <div>20.50%</div>
              <div>20.50%</div>
            </div>

            {/* Row 2 */}
            <div className="grid grid-cols-6 text-sm border-b py-2">
              <div className="col-span-2">Design</div>
              <div>20,230,450.00</div>
              <div>2000</div>
              <div>20.50%</div>
              <div>20.50%</div>
            </div>
          </div>

          {/* Payment + Total */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold">Payment instruction</h4>
              <p>
                Paypal email:{" "}
                <span className="text-muted-foreground"><EMAIL></span>
              </p>
              <p>
                Make checks payable to:{" "}
                <span className="text-muted-foreground">John Smith</span>
              </p>
              <p>
                Bank Transfer Routing (ABA):{" "}
                <span className="text-muted-foreground">*********</span>
              </p>

              <h4 className="font-semibold mt-4">Notes</h4>
              <p className="text-sm text-muted-foreground">
                Prototype-based programming is a style of object-oriented programming in
                which behaviour...
              </p>
            </div>

            <div className="text-right space-y-1">
              <p>
                Subtotal: <span className="font-medium">USD 8000.00</span>
              </p>
              <p>Discount (20%): USD 0.00</p>
              <p>Shipping Cost: USD 0.00</p>
              <p>Sales Tax: USD 450.00</p>
              <p className="font-semibold text-lg">Total: USD 8,480.00</p>
              <p>Amount Paid: USD 0.00</p>
              <div className="bg-primary text-primary-foreground font-semibold p-2 rounded-md inline-block">
                Balance Due: USD 8,480.00
              </div>
            </div>
          </div>

          {/* Signature */}
          <div className="text-right mt-6">
            <p className="italic text-muted-foreground">Signature</p>
            <div className="h-12 w-32 border-b border-muted mx-auto mt-2"></div>
          </div>
        </Card>
      </div>
    </div>
  );
}
