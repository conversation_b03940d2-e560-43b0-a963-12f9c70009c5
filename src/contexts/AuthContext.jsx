"use client";
import { API_BASE_URL } from "@/constants/url";
import { createContext, useContext, useEffect, useState } from "react";

const authContext = createContext({});

export function AuthContextProvider({ children }) {
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState(null);

  async function verifyToken() {
    setLoading(true);
    try {
      const res = await fetch(`/api/auth/verify`, {
        credentials: "include", // Set cookies
      });
      const data = await res.json();
      if (!res.ok) alert(data.message || "Login failed");
      setToken(data.output?.token);
      setUser(data?.user);
      return data;
    } catch (err) {
      setToken(null);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    verifyToken();
  }, []);

  async function Login(email, password) {
    setLoading(true);
    try {
      if (!email || !password) alert("Email and password are required.");

      const res = await fetch(`/api/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
        credentials: "include", // Set cookies
      });
      const data = await res.json();
      if (!res.ok) alert(data.message || "Login failed");
      // document.cookie = `token=${data.output?.token}; path=/; max-age=86400`; // 1 day
      setToken(data.output?.token);
      setUser(data.output?.user);
      return data;
    } catch (err) {
      alert(err.message || "Login error");
    } finally {
      setLoading(false);
    }
  }

  async function Register(body) {
    setLoading(true);
    try {
      const res = await fetch(`/api/auth/register`, {
        method: "POST",
        body: body,
        credentials: "include",
      });
      const data = await res.json();
      if (!res.ok) alert(data.message || "Registration failed");
      return data;
    } catch (err) {
      alert(err.message || "Registration error");
    } finally {
      setLoading(false);
    }
  }

  async function Logout() {
    try {
      const res = await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      });

      if (!res.ok) {
        const data = await res.json();
        console.log("Logout error:", data.message);
      }

      setToken(null);
    } catch (err) {
      alert(err.message || "Error occured while logging out...");
    }
  }

  async function ChangePassword(email, newPassword) {
    setLoading(true);
    try {
      const res = await fetch(`/api/auth/change-password`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, newPassword }),
        credentials: "include",
      });
      const data = await res.json();
      if (!res.ok) alert(data.message || "Failed to Change Password");
      return data;
    } catch (err) {
      alert(err.message || "Error occured while Changing Password");
    } finally {
      setLoading(false);
    }
  }

  const value = {
    token,
    user,
    loading,
    Login,
    Logout,
    ChangePassword,
    Register,
  };

  return <authContext.Provider value={value}>{children}</authContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(authContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthContextProvider");
  }
  return context;
};
