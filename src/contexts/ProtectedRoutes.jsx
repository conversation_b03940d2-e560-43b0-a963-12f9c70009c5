"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Mosaic } from "react-loading-indicators";

export default function ProtectedRoutes({ children }) {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set a timeout to simulate delay (e.g., 2 seconds)
    const timer = setTimeout(() => {
      if (!user?.id) {
        router.push("/login");
      }
      setLoading(false);
    }, 2000); // 2 seconds timeout

    return () => clearTimeout(timer);
  }, [user, router]);

  if (loading) {
    // You can return a spinner or loading UI here
    return (
      <main className="w-full h-screen flex items-center justify-center">
        <Mosaic color="#1e73f8" size="medium" text="" textColor="" />
      </main>
    );
  }

  return children;
}
