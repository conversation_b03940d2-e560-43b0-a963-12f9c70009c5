import { API_BASE_URL } from "@/constants/url";
import { toast } from "sonner";

export const fetchUserAccess = async () => {
  try {
    const res = await fetch(`/api/user_access`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(
      error.message || "Error occured while fetching Access Lists...."
    );
  }
};

export const fetchUserAccessByFilter = async (filter) => {
  try {
    const res = await fetch(`/api/user_access${filter}`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(
      error.message || "Error occured while fetching Access Lists...."
    );
  }
};

export const addOrUpdateUserAccess = async (body) => {
  try {
    const res = await fetch(`/api/user_access`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(
      error.message || "Error occured while updating Access Lists...."
    );
  }
};

export const deleteUserAccess = async (id) => {
  try {
    const res = await fetch(`/api/user_access?id=${id}`, {
      method: "DELETE",
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(
      error.message || "Error occured while deleting Access Lists...."
    );
  }
};
