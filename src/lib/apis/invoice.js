export const fetchInvoices = async () => {
  try {
    const res = await fetch(`/api/invoices`, {
      credentials: "include", // Set cookies
    });
    const data = await res.json();
    return data;
  } catch (error) {
    return "Error occured while fetching Invoices....";
  }
};

export const fetchInvoicesByFilter = async (filter) => {
  try {
    const res = await fetch(`/api/invoices${filter}`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    return error.message || "Error occured while fetching Invoices....";
  }
};

export const createInvoice = async (body) => {
  try {
    const res = await fetch(`/api/invoices`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    return error.message || "Error occured while creating Invoice....";
  }
};

export const updateInvoice = async (body) => {
  try {
    const res = await fetch(`/api/invoices`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    return error.message || "Error occured while updating Invoice....";
  }
};

export const deleteInvoice = async (id) => {
  try {
    const res = await fetch(`/api/invoices?id=${id}`, {
      method: "DELETE",
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    return error.message || "Error occured while deleting User....";
  }
};
