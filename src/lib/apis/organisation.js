export const fetchOrganisations = async () => {
  try {
    const res = await fetch(`/api/organisation`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    throw new Error(
      error.message || "Error occured while fetching Organisations...."
    );
  }
};

export const fetchOrganisationsByFilter = async (filter) => {
  try {
    const res = await fetch(`/api/organisation${filter}`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    throw new Error(
      error.message || "Error occured while fetching Organisations...."
    );
  }
};

export const createOrganisation = async (body) => {
  try {
    const res = await fetch(`/api/organisation`, {
      method: "POST",
      body: body,
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    throw new Error(
      error.message || "Error occured while creating Organisation...."
    );
  }
};

export const updateOrganisation = async (body) => {
  try {
    const res = await fetch(`/api/organisation`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    throw new Error(
      error.message || "Error occured while updating Organisation...."
    );
  }
};

export const deleteOrganisation = async (id) => {
  try {
    const res = await fetch(`/api/organisation?id=${id}`, {
      method: "DELETE",
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    throw new Error(
      error.message || "Error occured while deleting Organisation...."
    );
  }
};

// FormData
export const updateOrgFilesUrl = async (body) => {
  try {
    const res = await fetch(`/api/organisation`, {
      method: "PUT",
      body: body,
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    throw new Error(
      error.message || "Error occured while updating Organisation...."
    );
  }
};
