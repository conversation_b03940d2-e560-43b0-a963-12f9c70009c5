import { API_BASE_URL } from "@/constants/url";
import { toast } from "sonner";

export const fetchUsers = async () => {
  try {
    const res = await fetch(`/api/users`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(error.message || "Error occured while fetching Users....");
  }
};

export const fetchUsersByFilter = async (filter) => {
  try {
    const res = await fetch(`/api/users${filter}`, {
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(error.message || "Error occured while fetching Users....");
  }
};

export const updateUserInfo = async (body) => {
  try {
    const res = await fetch(`/api/users`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(
      error.message || "Error occured while updating User Details...."
    );
  }
};

// FormData
export const updateUserProfile = async (body) => {
  try {
    const res = await fetch(`/api/users`, {
      method: "PUT",
      body: body,
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(error.message || "Error occured while updating User....");
  }
};

export const deleteUser = async (id) => {
  try {
    const res = await fetch(`/api/users?id=${id}`, {
      method: "DELETE",
      credentials: "include", // Set cookies
    });
    const data = res.json();
    return data;
  } catch (error) {
    toast.error(error.message || "Error occured while deleting User....");
  }
};

export const checkIsAdmin = async (userId) => {
  try {
    const res = await fetch(`/api/users?id=${userId}&role=admin`, {
      credentials: "include",
    });
    const data = res.json();
    console.log(data);
    if (data?.returncode || res.ok) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    toast.error(error.message || "Error occured while fetching Users....");
  }
};
